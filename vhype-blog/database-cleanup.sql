-- VoiceHype Blog Database Cleanup and RLS Setup
-- Run this script to prepare the database for the public blog website

-- ==================== CLEANUP ====================

-- Drop article_reactions table (removing reactions feature)
DROP TABLE IF EXISTS blog.article_reactions CASCADE;

-- Drop reactions table if it exists (removing reactions feature)
DROP TABLE IF EXISTS blog.reactions CASCADE;

-- ==================== RLS POLICIES ====================

-- Enable RLS on articles table if not already enabled
ALTER TABLE blog.articles ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Public can view published articles" ON blog.articles;
DROP POLICY IF EXISTS "Authors can view their own articles" ON blog.articles;
DROP POLICY IF EXISTS "Ad<PERSON> can view all articles" ON blog.articles;

-- Create policy for public access to published articles only
CREATE POLICY "Public can view published articles" ON blog.articles
    FOR SELECT
    USING (status = 'published');

-- Create policy for authenticated users to view their own articles (including drafts)
CREATE POLICY "Authors can view their own articles" ON blog.articles
    FOR SELECT
    USING (auth.uid() = author_id);

-- Create policy for admin/editor roles to view all articles
CREATE POLICY "Admins can view all articles" ON blog.articles
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM blog.profiles 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'editor')
        )
    );

-- ==================== FUNCTION UPDATES ====================

-- Update the get_article_with_view_increment function to ensure it only returns published articles
CREATE OR REPLACE FUNCTION blog.get_article_with_view_increment(slug_param text)
 RETURNS TABLE(
    id uuid, 
    title text, 
    slug text, 
    content text, 
    excerpt text, 
    cover_image_url text, 
    author_id uuid, 
    status text, 
    published_at timestamp with time zone, 
    created_at timestamp with time zone, 
    updated_at timestamp with time zone, 
    meta_title text, 
    meta_description text, 
    featured boolean, 
    view_count integer, 
    author_username text, 
    author_name text, 
    author_avatar text, 
    author_bio text
 )
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
declare
  article_record record;
begin
  -- Get the article with author info (ONLY PUBLISHED ARTICLES)
  select 
    a.*,
    p.username as author_username,
    p.full_name as author_name,
    p.avatar_url as author_avatar,
    p.bio as author_bio
  into article_record
  from blog.articles a
  join blog.profiles p on a.author_id = p.id
  where a.slug = slug_param 
    and a.status = 'published'  -- Ensure only published articles are returned
    and a.published_at IS NOT NULL;  -- Additional safety check
  
  -- If article exists, increment view count
  if article_record.id is not null then
    -- Upsert view count
    insert into blog.article_views (article_id, view_count, updated_at)
    values (article_record.id, 1, now())
    on conflict (article_id)
    do update set 
      view_count = blog.article_views.view_count + 1,
      updated_at = now();
    
    -- Get current view count
    select coalesce(av.view_count, 0) into article_record.view_count
    from blog.article_views av
    where av.article_id = article_record.id;
    
    -- Return the article data
    return query select 
      article_record.id,
      article_record.title,
      article_record.slug,
      article_record.content,
      article_record.excerpt,
      article_record.cover_image_url,
      article_record.author_id,
      article_record.status,
      article_record.published_at,
      article_record.created_at,
      article_record.updated_at,
      article_record.meta_title,
      article_record.meta_description,
      article_record.featured,
      coalesce(article_record.view_count, 0),
      article_record.author_username,
      article_record.author_name,
      article_record.author_avatar,
      article_record.author_bio;
  end if;
end;
$function$;

-- ==================== ADDITIONAL FUNCTIONS ====================

-- Function to get published articles for homepage
CREATE OR REPLACE FUNCTION blog.get_published_articles(
    limit_count integer DEFAULT 10,
    offset_count integer DEFAULT 0,
    order_by text DEFAULT 'published_at'
)
RETURNS TABLE(
    id uuid,
    title text,
    slug text,
    excerpt text,
    cover_image_url text,
    author_id uuid,
    published_at timestamp with time zone,
    created_at timestamp with time zone,
    meta_title text,
    meta_description text,
    featured boolean,
    view_count integer,
    author_username text,
    author_name text,
    author_avatar text
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
begin
    return query
    select 
        a.id,
        a.title,
        a.slug,
        a.excerpt,
        a.cover_image_url,
        a.author_id,
        a.published_at,
        a.created_at,
        a.meta_title,
        a.meta_description,
        a.featured,
        coalesce(av.view_count, 0) as view_count,
        p.username as author_username,
        p.full_name as author_name,
        p.avatar_url as author_avatar
    from blog.articles a
    join blog.profiles p on a.author_id = p.id
    left join blog.article_views av on a.id = av.article_id
    where a.status = 'published' 
        and a.published_at IS NOT NULL
    order by 
        case 
            when order_by = 'published_at' then a.published_at
            when order_by = 'view_count' then av.view_count::timestamp with time zone
            when order_by = 'created_at' then a.created_at
            else a.published_at
        end desc
    limit limit_count
    offset offset_count;
end;
$function$;

-- Function to search published articles
CREATE OR REPLACE FUNCTION blog.search_published_articles(search_term text)
RETURNS TABLE(
    id uuid,
    title text,
    slug text,
    excerpt text,
    cover_image_url text,
    author_id uuid,
    published_at timestamp with time zone,
    meta_title text,
    meta_description text,
    featured boolean,
    view_count integer,
    author_username text,
    author_name text,
    author_avatar text
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
begin
    return query
    select 
        a.id,
        a.title,
        a.slug,
        a.excerpt,
        a.cover_image_url,
        a.author_id,
        a.published_at,
        a.meta_title,
        a.meta_description,
        a.featured,
        coalesce(av.view_count, 0) as view_count,
        p.username as author_username,
        p.full_name as author_name,
        p.avatar_url as author_avatar
    from blog.articles a
    join blog.profiles p on a.author_id = p.id
    left join blog.article_views av on a.id = av.article_id
    where a.status = 'published' 
        and a.published_at IS NOT NULL
        and (
            a.title ILIKE '%' || search_term || '%' 
            or a.excerpt ILIKE '%' || search_term || '%'
            or a.content ILIKE '%' || search_term || '%'
        )
    order by a.published_at desc;
end;
$function$;

-- Grant execute permissions to anon role for public access
GRANT EXECUTE ON FUNCTION blog.get_article_with_view_increment(text) TO anon;
GRANT EXECUTE ON FUNCTION blog.get_published_articles(integer, integer, text) TO anon;
GRANT EXECUTE ON FUNCTION blog.search_published_articles(text) TO anon;

-- ==================== VERIFICATION ====================

-- Verify the setup
SELECT 'Database cleanup and RLS setup completed successfully!' as status;
