# VoiceHype Blog

A modern, SEO-optimized blog built with Astro for VoiceHype - the voice-to-prompt technology platform for developers.

## 🚀 Features

- **SEO Optimized**: Built-in meta tags, Open Graph, structured data, and sitemap generation
- **Custom Markdown Parser**: Supports custom components like `<Tip>`, `<TryOutVoiceHype>`, and `<SubscribeNewsletter>`
- **Dark/Light Mode**: Automatic theme switching with user preference persistence
- **Search Functionality**: Full-text search across all published articles
- **Responsive Design**: Mobile-first design with Tailwind CSS
- **Performance Focused**: Optimized for Core Web Vitals and fast loading
- **Database Integration**: Connected to Supabase with Row Level Security
- **Loading States**: Shimmer effects and skeleton loaders for better UX

## 🛠️ Tech Stack

- **Framework**: [Astro](https://astro.build/) - Static site generator with islands architecture
- **Styling**: [Tailwind CSS](https://tailwindcss.com/) - Utility-first CSS framework
- **UI Components**: [shadcn/ui](https://ui.shadcn.com/) - Reusable component library
- **Database**: [Supabase](https://supabase.io/) - PostgreSQL with real-time features
- **Deployment**: [Netlify](https://netlify.com/) - Static site hosting with edge functions
- **Code Highlighting**: [Prism.js](https://prismjs.com/) - Syntax highlighting for code blocks

## 🚦 Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Supabase database with blog schema

### Installation

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```

   Update `.env` with your Supabase credentials

3. **Set up the database**
   ```bash
   # Run the SQL commands from database-cleanup.sql in your Supabase SQL editor
   ```

4. **Start the development server**
   ```bash
   npm run dev
   ```

## 🧞 Commands

| Command                   | Action                                           |
| :------------------------ | :----------------------------------------------- |
| `npm install`             | Installs dependencies                            |
| `npm run dev`             | Starts local dev server at `localhost:4321`      |
| `npm run build`           | Build your production site to `./dist/`          |
| `npm run preview`         | Preview your build locally, before deploying     |

## 🚀 Deployment

See [DEPLOYMENT.md](./DEPLOYMENT.md) for detailed deployment instructions.

## 📄 License

This project is proprietary software owned by VoiceHype. All rights reserved.

---

Built with ❤️ by the VoiceHype team