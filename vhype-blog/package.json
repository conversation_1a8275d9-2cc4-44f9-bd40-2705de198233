{"name": "vhype-blog", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro"}, "dependencies": {"@astrojs/mdx": "^4.3.4", "@astrojs/react": "^4.3.0", "@astrojs/rss": "^4.0.12", "@astrojs/sitemap": "^3.5.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@supabase/supabase-js": "^2.56.0", "@tailwindcss/vite": "^4.1.12", "@types/prismjs": "^1.26.5", "@types/react": "^19.1.11", "@types/react-dom": "^19.1.8", "astro": "^5.13.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.542.0", "prismjs": "^1.30.0", "react": "^19.1.1", "react-dom": "^19.1.1", "sharp": "^0.34.2", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.12"}, "devDependencies": {"tw-animate-css": "^1.3.7"}}