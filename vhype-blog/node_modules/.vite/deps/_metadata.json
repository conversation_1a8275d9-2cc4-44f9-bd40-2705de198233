{"hash": "90abc288", "configHash": "79409de7", "lockfileHash": "bddb737c", "browserHash": "06283921", "optimized": {"@astrojs/react/client.js": {"src": "../../@astrojs/react/dist/client.js", "file": "@astrojs_react_client__js.js", "fileHash": "089e8809", "needsInterop": false}, "react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "5e7eb586", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "f6d86034", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "e749aa6a", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "43e9ca9c", "needsInterop": true}, "astro > cssesc": {"src": "../../cssesc/cssesc.js", "file": "astro___cssesc.js", "fileHash": "5a829561", "needsInterop": true}, "astro > aria-query": {"src": "../../aria-query/lib/index.js", "file": "astro___aria-query.js", "fileHash": "0d23aef7", "needsInterop": true}, "astro > axobject-query": {"src": "../../axobject-query/lib/index.js", "file": "astro___axobject-query.js", "fileHash": "783d0de8", "needsInterop": true}, "@radix-ui/react-avatar": {"src": "../../@radix-ui/react-avatar/dist/index.mjs", "file": "@radix-ui_react-avatar.js", "fileHash": "8d0a267e", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "317a5f8d", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "06a7be2b", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "a54c4944", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "ccd40f78", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "eb0fd146", "needsInterop": false}}, "chunks": {"chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-ZKHRRQVN": {"file": "chunk-ZKHRRQVN.js"}, "chunk-SQG4LW6N": {"file": "chunk-SQG4LW6N.js"}, "chunk-AUFLGUIX": {"file": "chunk-AUFLGUIX.js"}, "chunk-XO4JX7VM": {"file": "chunk-XO4JX7VM.js"}, "chunk-PSQR3SVX": {"file": "chunk-PSQR3SVX.js"}, "chunk-5WRI5ZAA": {"file": "chunk-5WRI5ZAA.js"}}}