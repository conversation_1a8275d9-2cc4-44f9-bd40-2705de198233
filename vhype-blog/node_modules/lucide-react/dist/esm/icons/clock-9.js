/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M12 6v6H8", key: "u39vzm" }],
  ["circle", { cx: "12", cy: "12", r: "10", key: "1mglay" }]
];
const Clock9 = createLucideIcon("clock-9", __iconNode);

export { __iconNode, Clock9 as default };
//# sourceMappingURL=clock-9.js.map
