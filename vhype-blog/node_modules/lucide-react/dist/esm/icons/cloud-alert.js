/**
 * @license lucide-react v0.542.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M12 12v4", key: "tww15h" }],
  ["path", { d: "M12 20h.01", key: "zekei9" }],
  ["path", { d: "M17 18h.5a1 1 0 0 0 0-9h-1.79A7 7 0 1 0 7 17.708", key: "xsb5ju" }]
];
const CloudAlert = createLucideIcon("cloud-alert", __iconNode);

export { __iconNode, CloudAlert as default };
//# sourceMappingURL=cloud-alert.js.map
