{"$ref": "#/definitions/blog", "definitions": {"blog": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "pubDate": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "string", "format": "date"}, {"type": "integer", "format": "unix-time"}]}, "updatedDate": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "string", "format": "date"}, {"type": "integer", "format": "unix-time"}]}, "heroImage": {"type": "string"}, "$schema": {"type": "string"}}, "required": ["title", "description", "pubDate"], "additionalProperties": false}}, "$schema": "http://json-schema.org/draft-07/schema#"}