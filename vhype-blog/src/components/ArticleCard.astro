---
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { formatDate } from '../lib/supabase';
import type { BlogArticlePreview } from '../lib/supabase';

export interface Props {
  article: BlogArticlePreview;
}

const { article } = Astro.props;
---

<Card class="group hover:shadow-lg transition-shadow duration-200">
  {article.cover_image_url && (
    <div class="aspect-video overflow-hidden rounded-t-lg">
      <img
        src={article.cover_image_url}
        alt={article.title}
        class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
        loading="lazy"
      />
    </div>
  )}

  <CardHeader class="pb-3">
    <div class="flex items-center gap-2 text-sm text-muted-foreground mb-2">
      <time datetime={article.published_at}>
        {formatDate(article.published_at)}
      </time>
      <span>•</span>
      <Badge variant="secondary" class="text-xs">
        {article.view_count} views
      </Badge>
    </div>

    <CardTitle class="line-clamp-2 group-hover:text-primary transition-colors">
      <a href={`/${article.slug}`}>
        {article.title}
      </a>
    </CardTitle>

    <CardDescription class="line-clamp-3">
      {article.excerpt}
    </CardDescription>
  </CardHeader>

  <CardContent class="pt-0">
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-2">
        <Avatar class="h-6 w-6">
          <AvatarImage src={article.author_avatar} alt={article.author_name || 'Author'} />
          <AvatarFallback class="text-xs">
            {(article.author_name || 'A').split(' ').map(n => n[0]).join('').toUpperCase()}
          </AvatarFallback>
        </Avatar>
        <span class="text-sm text-muted-foreground">
          {article.author_name}
        </span>
      </div>

      <a
        href={`/${article.slug}`}
        class="text-sm font-medium text-primary hover:underline"
      >
        Read more →
      </a>
    </div>
  </CardContent>
</Card>

<style>
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
