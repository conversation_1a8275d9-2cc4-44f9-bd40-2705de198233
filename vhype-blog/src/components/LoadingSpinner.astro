---
export interface Props {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
}

const { size = 'md', text = 'Loading...' } = Astro.props;

const sizeClasses = {
  sm: 'w-4 h-4',
  md: 'w-8 h-8',
  lg: 'w-12 h-12'
};
---

<div class="flex flex-col items-center justify-center p-8">
  <div class={`animate-spin rounded-full border-2 border-gray-300 border-t-blue-600 ${sizeClasses[size]}`}></div>
  {text && (
    <p class="mt-4 text-gray-600 dark:text-gray-400 text-sm">
      {text}
    </p>
  )}
</div>

<style>
  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
  
  .animate-spin {
    animation: spin 1s linear infinite;
  }
</style>
