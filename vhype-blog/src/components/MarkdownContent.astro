---
import { markdownProcessor } from '../markdown';
import Tip from './blog/Tip.astro';
import TryOutVoiceHype from './blog/TryOutVoiceHype.astro';
import SubscribeNewsletter from './blog/SubscribeNewsletter.astro';

export interface Props {
  content: string;
}

const { content } = Astro.props;

// Process markdown to HTML
const html = markdownProcessor.process(content);

// Component registry for replacing custom component tags
const componentRegistry = {
  'TryOutVoiceHype': TryOutVoiceHype,
  'Tip': Tip,
  'SubscribeNewsletter': SubscribeNewsletter,
};

// Parse HTML and extract custom components
function parseHtmlWithComponents(html: string) {
  const items: Array<{
    type: 'html' | 'component'
    content?: string
    component?: string
    props?: Record<string, any>
  }> = [];

  // Split HTML by custom component tags
  const componentRegex = /<(TryOutVoiceHype|Tip|SubscribeNewsletter)([^>]*?)\/?>(?:<\/\1>)?/g;
  
  let lastIndex = 0;
  let match;

  while ((match = componentRegex.exec(html)) !== null) {
    // Add HTML content before the component
    if (match.index > lastIndex) {
      const htmlContent = html.slice(lastIndex, match.index).trim();
      if (htmlContent) {
        items.push({
          type: 'html',
          content: htmlContent
        });
      }
    }

    // Parse component attributes
    const componentName = match[1];
    const attributesStr = match[2] || '';
    const props = parseAttributes(attributesStr);

    items.push({
      type: 'component',
      component: componentName,
      props
    });

    lastIndex = match.index + match[0].length;
  }

  // Add remaining HTML content
  if (lastIndex < html.length) {
    const htmlContent = html.slice(lastIndex).trim();
    if (htmlContent) {
      items.push({
        type: 'html',
        content: htmlContent
      });
    }
  }

  return items;
}

function parseAttributes(attributesStr: string): Record<string, any> {
  const attributes: Record<string, any> = {};
  const attrRegex = /(\w+)=(?:"([^"]*)"|'([^']*)'|([^\s>]+))/g;
  let match;
  
  while ((match = attrRegex.exec(attributesStr)) !== null) {
    const key = match[1];
    const value = match[2] || match[3] || match[4] || '';
    attributes[key] = value;
  }
  
  return attributes;
}

const parsedItems = parseHtmlWithComponents(html);
---

<div class="markdown-content dark:prose-invert max-w-none prose prose-lg">
  {parsedItems.map((item, index) => {
    if (item.type === 'html') {
      return <Fragment set:html={item.content} />;
    } else if (item.type === 'component' && item.component) {
      const Component = componentRegistry[item.component as keyof typeof componentRegistry];
      if (Component) {
        return <Component {...(item.props || {})} />;
      }
    }
    return null;
  })}
</div>

<style>
  /* Custom styles for markdown content */
  .markdown-content {
    color: #111827;
  }

  .dark .markdown-content {
    color: #f9fafb;
  }

  .markdown-content h1 {
    font-size: 1.875rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    margin-top: 2rem;
    color: #111827;
  }

  .dark .markdown-content h1 {
    color: #f9fafb;
  }

  .markdown-content h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    margin-top: 1.5rem;
    color: #111827;
  }

  .dark .markdown-content h2 {
    color: #f9fafb;
  }

  .markdown-content h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    margin-top: 1.25rem;
    color: #111827;
  }

  .dark .markdown-content h3 {
    color: #f9fafb;
  }

  .markdown-content h4 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    margin-top: 1rem;
    color: #111827;
  }

  .dark .markdown-content h4 {
    color: #f9fafb;
  }

  .markdown-content p {
    margin-bottom: 1rem;
    line-height: 1.625;
    color: #374151;
  }

  .dark .markdown-content p {
    color: #d1d5db;
  }

  .markdown-content ul,
  .markdown-content ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
  }

  .markdown-content li {
    margin-bottom: 0.5rem;
    color: #374151;
  }

  .dark .markdown-content li {
    color: #d1d5db;
  }

  .markdown-content blockquote {
    border-left: 4px solid #3b82f6;
    padding-left: 1rem;
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    margin: 1rem 0;
    background-color: #eff6ff;
    color: #1e3a8a;
    font-style: italic;
  }

  .dark .markdown-content blockquote {
    background-color: #172554;
    color: #dbeafe;
  }

  .markdown-content code {
    background-color: #f3f4f6;
    padding: 0.125rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
    color: #1f2937;
  }

  .dark .markdown-content code {
    background-color: #1f2937;
    color: #e5e7eb;
  }

  .markdown-content pre {
    background-color: #111827;
    padding: 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    margin: 1rem 0;
  }

  .dark .markdown-content pre {
    background-color: #030712;
  }

  .markdown-content pre code {
    background-color: transparent;
    padding: 0;
    color: #f9fafb;
  }

  .markdown-content a {
    color: #2563eb;
    text-decoration: underline;
  }

  .markdown-content a:hover {
    color: #1d4ed8;
  }

  .dark .markdown-content a {
    color: #60a5fa;
  }

  .dark .markdown-content a:hover {
    color: #93c5fd;
  }

  .markdown-content img {
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    margin: 1rem 0;
    max-width: 100%;
    height: auto;
  }

  .markdown-content hr {
    border-color: #d1d5db;
    margin: 2rem 0;
  }

  .dark .markdown-content hr {
    border-color: #374151;
  }

  .markdown-content strong {
    font-weight: 600;
    color: #111827;
  }

  .dark .markdown-content strong {
    color: #f9fafb;
  }

  .markdown-content em {
    font-style: italic;
    color: #1f2937;
  }

  .dark .markdown-content em {
    color: #e5e7eb;
  }
</style>
