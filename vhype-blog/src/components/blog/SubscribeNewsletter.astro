---
import { <PERSON>, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
---

<Card className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950 dark:to-emerald-950 border-green-200 dark:border-green-800 my-6">
  <CardContent className="p-6">
  <div class="flex items-start gap-4">
    <div class="bg-green-500 text-white p-3 rounded-lg">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
      </svg>
    </div>
    <div class="flex-1">
      <h3 class="text-lg font-semibold text-green-900 dark:text-green-100 mb-2">
        Subscribe to VoiceHype Newsletter
      </h3>
      <p class="text-green-700 dark:text-green-300 mb-4">
        Get the latest updates on voice-to-prompt technology, AI development tips, and VoiceHype features delivered to your inbox.
      </p>
      <form class="flex flex-col sm:flex-row gap-3" id="newsletter-form">
        <Input
          type="email"
          placeholder="Enter your email address"
          required
          className="flex-1"
          id="newsletter-email"
        />
        <Button type="submit">
          Subscribe
        </Button>
      </form>
      <p class="text-sm text-green-600 dark:text-green-400 mt-2">
        No spam, unsubscribe at any time.
      </p>
    </div>
  </CardContent>
</Card>

<script>
  // Newsletter subscription functionality
  document.addEventListener('DOMContentLoaded', () => {
    const form = document.getElementById('newsletter-form') as HTMLFormElement;
    const emailInput = document.getElementById('newsletter-email') as HTMLInputElement;
    
    if (form && emailInput) {
      form.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const email = emailInput.value.trim();
        if (!email) return;
        
        try {
          // TODO: Implement newsletter subscription API call
          // For now, just show a success message
          alert('Thank you for subscribing! We\'ll be in touch soon.');
          emailInput.value = '';
        } catch (error) {
          console.error('Newsletter subscription error:', error);
          alert('Sorry, there was an error subscribing. Please try again later.');
        }
      });
    }
  });
</script>
