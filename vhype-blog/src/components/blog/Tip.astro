---
import { Card, CardContent } from "@/components/ui/card";

export interface Props {
  title?: string;
  content?: string;
}

const { title = 'Tip', content = '' } = Astro.props;
---

<Card className="bg-amber-50 dark:bg-amber-950 border-amber-200 dark:border-amber-800 my-4">
  <CardContent className="p-4">
  <div class="flex items-start gap-3">
    <div class="bg-amber-500 text-white p-2 rounded-lg flex-shrink-0">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
      </svg>
    </div>
    <div class="flex-1">
      <h4 class="font-semibold text-amber-900 dark:text-amber-100 mb-1">
        {title}
      </h4>
      <div class="text-amber-800 dark:text-amber-200">
        {content ? (
          <p>{content}</p>
        ) : (
          <slot />
        )}
      </div>
    </div>
  </CardContent>
</Card>
