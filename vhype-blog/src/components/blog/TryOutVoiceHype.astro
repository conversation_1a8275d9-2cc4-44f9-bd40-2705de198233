---
import { <PERSON>, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
---

<Card className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950 dark:to-indigo-950 border-blue-200 dark:border-blue-800 my-6">
  <CardContent className="p-6">
  <div class="flex items-start gap-4">
    <div class="bg-blue-500 text-white p-3 rounded-lg">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
      </svg>
    </div>
    <div class="flex-1">
      <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
        Try VoiceHype - Voice-to-Prompt for Developers
      </h3>
      <p class="text-blue-700 dark:text-blue-300 mb-4">
        Transform your voice into optimized prompts for LLMs. Perfect for developers working with AI tools.
      </p>
      <div class="flex flex-wrap gap-3">
        <Button asChild>
          <a
            href="https://voicehype.ai"
            target="_blank"
            rel="noopener noreferrer"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
            </svg>
            Try VoiceHype
          </a>
        </Button>
        <Button variant="secondary" asChild>
          <a
            href="https://marketplace.visualstudio.com/items?itemName=voicehype.voicehype"
            target="_blank"
            rel="noopener noreferrer"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            VS Code Extension
          </a>
        </Button>
      </div>
    </div>
  </CardContent>
</Card>
