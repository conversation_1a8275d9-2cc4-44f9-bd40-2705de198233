---
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";

// Get current path for active link highlighting
const currentPath = Astro.url.pathname;

// Navigation items
const navItems = [
  { href: "/", label: "Home" },
  { href: "/search", label: "Search" },
  { href: "/about", label: "About" },
];

// Check if a nav item is active
function isActive(href: string): boolean {
  if (href === "/") {
    return currentPath === "/";
  }
  return currentPath.startsWith(href);
}
---

<header class="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
  <div class="container flex h-14 max-w-screen-2xl items-center">
    <!-- Logo Section -->
    <div class="mr-4 hidden md:flex">
      <a href="/" class="mr-6 flex items-center space-x-2">
        <svg 
          class="h-6 w-6 text-primary" 
          viewBox="0 0 24 24" 
          fill="none" 
          stroke="currentColor" 
          stroke-width="2"
          stroke-linecap="round" 
          stroke-linejoin="round"
        >
          <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z"/>
          <path d="Zm-9 9a9 9 0 1 0 18 0"/>
          <path d="m8 11 2 2 4-4"/>
        </svg>
        <span class="hidden font-bold sm:inline-block">
          VoiceHype Blog
        </span>
      </a>
    </div>

    <!-- Mobile Logo -->
    <div class="mr-4 flex md:hidden">
      <a href="/" class="flex items-center space-x-2">
        <svg 
          class="h-6 w-6 text-primary" 
          viewBox="0 0 24 24" 
          fill="none" 
          stroke="currentColor" 
          stroke-width="2"
          stroke-linecap="round" 
          stroke-linejoin="round"
        >
          <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z"/>
          <path d="Zm-9 9a9 9 0 1 0 18 0"/>
          <path d="m8 11 2 2 4-4"/>
        </svg>
        <span class="font-bold">VoiceHype</span>
      </a>
    </div>

    <!-- Navigation -->
    <div class="flex flex-1 items-center justify-between space-x-2 md:justify-end">
      <div class="w-full flex-1 md:w-auto md:flex-none">
        <nav class="flex items-center space-x-6 text-sm font-medium">
          {navItems.map((item) => (
            <a
              href={item.href}
              class={`transition-colors hover:text-foreground/80 ${
                isActive(item.href) 
                  ? "text-foreground font-semibold" 
                  : "text-foreground/60"
              }`}
            >
              {item.label}
            </a>
          ))}
          <Separator orientation="vertical" className="h-4" />
          <a
            href="https://voicehype.ai"
            target="_blank"
            rel="noopener noreferrer"
            class="text-foreground/60 transition-colors hover:text-foreground/80"
          >
            VoiceHype App
          </a>
        </nav>
      </div>

      <!-- Theme Toggle -->
      <div class="flex items-center space-x-2">
        <Button
          variant="ghost"
          size="sm"
          id="theme-toggle"
          class="h-8 w-8 px-0"
          aria-label="Toggle theme"
        >
          <svg 
            id="theme-toggle-dark-icon" 
            class="hidden h-4 w-4" 
            fill="currentColor" 
            viewBox="0 0 20 20"
          >
            <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
          </svg>
          <svg 
            id="theme-toggle-light-icon" 
            class="hidden h-4 w-4" 
            fill="currentColor" 
            viewBox="0 0 20 20"
          >
            <path d="M10 2L13.09 8.26L20 9L14 14.74L15.18 21.02L10 18L4.82 21.02L6 14.74L0 9L6.91 8.26L10 2Z"></path>
          </svg>
          <span class="sr-only">Toggle theme</span>
        </Button>
      </div>
    </div>
  </div>
</header>

<!-- Theme toggle script -->
<script>
  const themeToggle = document.getElementById('theme-toggle');
  const darkIcon = document.getElementById('theme-toggle-dark-icon');
  const lightIcon = document.getElementById('theme-toggle-light-icon');
  
  function updateThemeIcons() {
    if (document.documentElement.classList.contains('dark')) {
      darkIcon?.classList.add('hidden');
      lightIcon?.classList.remove('hidden');
    } else {
      darkIcon?.classList.remove('hidden');
      lightIcon?.classList.add('hidden');
    }
  }
  
  // Initialize icons
  updateThemeIcons();
  
  themeToggle?.addEventListener('click', () => {
    document.documentElement.classList.toggle('dark');
    const isDark = document.documentElement.classList.contains('dark');
    localStorage.setItem('theme', isDark ? 'dark' : 'light');
    updateThemeIcons();
  });
</script>
