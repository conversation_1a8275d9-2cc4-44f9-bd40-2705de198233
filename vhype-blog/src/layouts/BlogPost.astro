---
import BaseLayout from './BaseLayout.astro';
import MarkdownContent from '../components/MarkdownContent.astro';
import { formatDate, calculateReadingTime } from '../lib/supabase';
import type { BlogArticle } from '../lib/supabase';

export interface Props {
  article: BlogArticle;
}

const { article } = Astro.props;

const readingTime = calculateReadingTime(article.content || '');

// SEO data
const title = article.meta_title || article.title;
const description = article.meta_description || article.excerpt;
const image = article.cover_image_url;
const canonical = `https://blog.voicehype.ai/${article.slug}`;
---

<BaseLayout
  title={title}
  description={description}
  image={image}
  canonical={canonical}
  type="article"
  publishedTime={article.published_at || undefined}
  modifiedTime={article.updated_at}
  author={article.author_name}
>
  <article class="sm:px-6 lg:px-8 max-w-4xl px-4 py-8 mx-auto">
    <!-- Article Header -->
    <header class="mb-8">
      <!-- Hero Image -->
      {article.cover_image_url && (
        <div class="mb-8">
          <img
            src={article.cover_image_url}
            alt={article.title}
            class="sm:h-80 lg:h-96 object-cover w-full h-64 rounded-lg shadow-lg"
            loading="eager"
          />
        </div>
      )}

      <!-- Article Meta -->
      <div class="mb-6">
        <div class="dark:text-gray-400 flex items-center mb-4 text-sm text-gray-600">
          <time datetime={article.published_at}>
            {formatDate(article.published_at || article.created_at)}
          </time>
          <span class="mx-2">•</span>
          <span>{readingTime} min read</span>
          <span class="mx-2">•</span>
          <span>{article.view_count} views</span>
        </div>

        <!-- Title -->
        <h1 class="sm:text-4xl lg:text-5xl dark:text-gray-100 mb-4 text-3xl font-bold leading-tight text-gray-900">
          {article.title}
        </h1>

        <!-- Excerpt -->
        {article.excerpt && (
          <p class="dark:text-gray-400 mb-6 text-lg leading-relaxed text-gray-600">
            {article.excerpt}
          </p>
        )}

        <!-- Author Info -->
        <div class="flex items-center">
          {article.author_avatar && (
            <img
              src={article.author_avatar}
              alt={article.author_name}
              class="w-12 h-12 mr-4 rounded-full"
            />
          )}
          <div>
            <div class="dark:text-gray-100 font-medium text-gray-900">
              {article.author_name}
            </div>
            {article.author_bio && (
              <div class="dark:text-gray-400 text-sm text-gray-600">
                {article.author_bio}
              </div>
            )}
          </div>
        </div>
      </div>

      <!-- Divider -->
      <hr class="dark:border-gray-700 border-gray-200" />
    </header>

    <!-- Article Content -->
    <div class="mb-12">
      {article.content && (
        <MarkdownContent content={article.content} />
      )}
    </div>

    <!-- Article Footer -->
    <footer class="dark:border-gray-700 pt-8 border-t border-gray-200">
      <!-- Updated Date -->
      {article.updated_at !== article.created_at && (
        <div class="dark:text-gray-400 mb-6 text-sm text-gray-600">
          Last updated on {formatDate(article.updated_at)}
        </div>
      )}

      <!-- Share Section -->
      <div class="mb-8">
        <h3 class="mb-4 text-lg font-semibold">Share this article</h3>
        <div class="flex space-x-4">
          <a
            href={`https://twitter.com/intent/tweet?text=${encodeURIComponent(article.title)}&url=${encodeURIComponent(canonical)}`}
            target="_blank"
            rel="noopener noreferrer"
            class="hover:bg-blue-600 inline-flex items-center px-4 py-2 text-white transition-colors bg-blue-500 rounded-lg"
          >
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
              <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
            </svg>
            Twitter
          </a>

          <a
            href={`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(canonical)}`}
            target="_blank"
            rel="noopener noreferrer"
            class="hover:bg-blue-800 inline-flex items-center px-4 py-2 text-white transition-colors bg-blue-700 rounded-lg"
          >
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
              <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
            </svg>
            LinkedIn
          </a>
        </div>
      </div>

      <!-- Back to Blog -->
      <div>
        <a
          href="/"
          class="dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 inline-flex items-center text-blue-600 transition-colors"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          Back to all articles
        </a>
      </div>
    </footer>
  </article>
</BaseLayout>
