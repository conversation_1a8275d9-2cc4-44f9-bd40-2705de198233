---
import "../styles/global.css";

export interface Props {
  title: string;
  description: string;
  image?: string;
  canonical?: string;
  type?: 'website' | 'article';
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  tags?: string[];
}

const {
  title,
  description,
  image = '/og-default.jpg',
  canonical,
  type = 'website',
  publishedTime,
  modifiedTime,
  author,
  tags = []
} = Astro.props;

const siteUrl = import.meta.env.PUBLIC_SITE_URL || 'https://blog.voicehype.ai';
const siteName = import.meta.env.PUBLIC_SITE_NAME || 'VoiceHype Blog';
const fullTitle = title.includes(siteName) ? title : `${title} | ${siteName}`;
const canonicalUrl = canonical || new URL(Astro.url.pathname, siteUrl).href;
const imageUrl = image.startsWith('http') ? image : new URL(image, siteUrl).href;
---

<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />
    
    <!-- Primary Meta Tags -->
    <title>{fullTitle}</title>
    <meta name="title" content={fullTitle} />
    <meta name="description" content={description} />
    <link rel="canonical" href={canonicalUrl} />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content={type} />
    <meta property="og:url" content={canonicalUrl} />
    <meta property="og:title" content={fullTitle} />
    <meta property="og:description" content={description} />
    <meta property="og:image" content={imageUrl} />
    <meta property="og:site_name" content={siteName} />
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content={canonicalUrl} />
    <meta property="twitter:title" content={fullTitle} />
    <meta property="twitter:description" content={description} />
    <meta property="twitter:image" content={imageUrl} />
    
    <!-- Article specific meta tags -->
    {type === 'article' && (
      <>
        {publishedTime && <meta property="article:published_time" content={publishedTime} />}
        {modifiedTime && <meta property="article:modified_time" content={modifiedTime} />}
        {author && <meta property="article:author" content={author} />}
        {tags.map(tag => <meta property="article:tag" content={tag} />)}
      </>
    )}
    
    <!-- Structured Data -->
    <script type="application/ld+json" is:inline set:html={JSON.stringify({
      "@context": "https://schema.org",
      "@type": type === 'article' ? "BlogPosting" : "WebSite",
      "headline": title,
      "description": description,
      "image": imageUrl,
      "url": canonicalUrl,
      "datePublished": publishedTime,
      "dateModified": modifiedTime || publishedTime,
      "author": author ? {
        "@type": "Person",
        "name": author
      } : undefined,
      "publisher": {
        "@type": "Organization",
        "name": siteName,
        "url": siteUrl,
        "logo": {
          "@type": "ImageObject",
          "url": new URL('/favicon.svg', siteUrl).href
        }
      },
      "mainEntityOfPage": {
        "@type": "WebPage",
        "@id": canonicalUrl
      }
    })} />
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    
    <!-- Performance optimizations -->
    <link rel="dns-prefetch" href="https://supabase.voicehype.ai" />
    
    <!-- Theme detection script (inline for performance) -->
    <script is:inline>
      // Check for saved theme preference or default to 'light'
      const theme = localStorage.getItem('theme') || 'light';
      if (theme === 'dark') {
        document.documentElement.classList.add('dark');
      }
    </script>
  </head>
  
  <body class="dark:bg-gray-900 dark:text-gray-100 text-gray-900 transition-colors duration-200 bg-white">
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50 px-4 py-2 text-white bg-blue-600 rounded-md sr-only">
      Skip to main content
    </a>
    
    <div class="flex flex-col min-h-screen">
      <!-- Header -->
      <header class="sticky top-0 z-40 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div class="h-14 container flex items-center">
          <!-- Logo -->
          <div class="flex mr-4">
            <a href="/" class="flex items-center mr-6 space-x-2">
              <svg class="w-6 h-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
              <span class="sm:inline-block hidden font-bold">VoiceHype Blog</span>
            </a>
          </div>

          <!-- Navigation -->
          <div class="md:justify-end flex items-center justify-between flex-1 space-x-2">
            <nav class="flex items-center space-x-6 text-sm font-medium">
              <a href="/" class="hover:text-foreground/80 text-foreground/60 transition-colors">
                Home
              </a>
              <a href="/search" class="hover:text-foreground/80 text-foreground/60 transition-colors">
                Search
              </a>
              <a href="/about" class="hover:text-foreground/80 text-foreground/60 transition-colors">
                About
              </a>
              <a href="https://voicehype.ai" target="_blank" rel="noopener noreferrer" class="hover:text-foreground/80 text-foreground/60 transition-colors">
                VoiceHype App
              </a>
            </nav>

            <!-- Theme toggle -->
            <button
              id="theme-toggle"
              class="focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border-input hover:bg-accent hover:text-accent-foreground h-9 w-9 inline-flex items-center justify-center text-sm font-medium transition-colors bg-transparent border rounded-md shadow-sm"
              aria-label="Toggle theme"
            >
              <svg id="theme-toggle-dark-icon" class="hidden w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
              </svg>
              <svg id="theme-toggle-light-icon" class="hidden w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10 2L13.09 8.26L20 9L14 14.74L15.18 21.02L10 18L4.82 21.02L6 14.74L0 9L6.91 8.26L10 2Z"></path>
              </svg>
            </button>
          </div>
        </div>
      </header>
      
      <!-- Main content -->
      <main id="main-content" class="flex-1">
        <slot />
      </main>
      
      <!-- Footer -->
      <footer class="bg-gray-50 dark:bg-gray-800 dark:border-gray-700 border-t border-gray-200">
        <div class="max-w-7xl sm:px-6 lg:px-8 px-4 py-12 mx-auto">
          <div class="md:grid-cols-3 grid grid-cols-1 gap-8">
            <!-- About -->
            <div>
              <h3 class="mb-4 text-lg font-semibold">VoiceHype Blog</h3>
              <p class="dark:text-gray-400 mb-4 text-gray-600">
                Insights and tutorials on voice-to-prompt technology, AI development, and the future of developer tools.
              </p>
              <div class="flex space-x-4">
                <a href="https://voicehype.ai" target="_blank" rel="noopener noreferrer" class="dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-blue-600">
                  VoiceHype App
                </a>
                <a href="https://marketplace.visualstudio.com/items?itemName=voicehype.voicehype" target="_blank" rel="noopener noreferrer" class="dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-blue-600">
                  VS Code Extension
                </a>
              </div>
            </div>
            
            <!-- Quick Links -->
            <div>
              <h3 class="mb-4 text-lg font-semibold">Quick Links</h3>
              <ul class="space-y-2">
                <li><a href="/" class="dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 text-gray-600">Home</a></li>
                <li><a href="/about" class="dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 text-gray-600">About</a></li>
                <li><a href="/rss.xml" class="dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 text-gray-600">RSS Feed</a></li>
              </ul>
            </div>
            
            <!-- Contact -->
            <div>
              <h3 class="mb-4 text-lg font-semibold">Connect</h3>
              <p class="dark:text-gray-400 mb-2 text-gray-600">
                Stay updated with the latest from VoiceHype
              </p>
              <p class="dark:text-gray-500 text-sm text-gray-500">
                © 2024 VoiceHype. All rights reserved.
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
    
    <!-- Theme toggle script -->
    <script>
      const themeToggle = document.getElementById('theme-toggle');
      const darkIcon = document.getElementById('theme-toggle-dark-icon');
      const lightIcon = document.getElementById('theme-toggle-light-icon');
      
      function updateThemeIcons() {
        if (document.documentElement.classList.contains('dark')) {
          darkIcon?.classList.add('hidden');
          lightIcon?.classList.remove('hidden');
        } else {
          darkIcon?.classList.remove('hidden');
          lightIcon?.classList.add('hidden');
        }
      }
      
      // Initialize icons
      updateThemeIcons();
      
      themeToggle?.addEventListener('click', () => {
        document.documentElement.classList.toggle('dark');
        const isDark = document.documentElement.classList.contains('dark');
        localStorage.setItem('theme', isDark ? 'dark' : 'light');
        updateThemeIcons();
      });
    </script>
  </body>
</html>
