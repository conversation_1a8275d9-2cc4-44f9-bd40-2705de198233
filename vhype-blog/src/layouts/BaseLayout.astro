---
import "../styles/global.css";
import Navbar from "../components/Navbar.astro";

export interface Props {
  title: string;
  description: string;
  image?: string;
  canonical?: string;
  type?: 'website' | 'article';
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  tags?: string[];
}

const {
  title,
  description,
  image = '/og-default.jpg',
  canonical,
  type = 'website',
  publishedTime,
  modifiedTime,
  author,
  tags = []
} = Astro.props;

const siteUrl = (import.meta as any).env.PUBLIC_SITE_URL || 'https://blog.voicehype.ai';
const siteName = (import.meta as any).env.PUBLIC_SITE_NAME || 'VoiceHype Blog';
const fullTitle = title.includes(siteName) ? title : `${title} | ${siteName}`;
const canonicalUrl = canonical || new URL(Astro.url.pathname, siteUrl).href;
const imageUrl = image.startsWith('http') ? image : new URL(image, siteUrl).href;
---

<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />
    
    <!-- Primary Meta Tags -->
    <title>{fullTitle}</title>
    <meta name="title" content={fullTitle} />
    <meta name="description" content={description} />
    <link rel="canonical" href={canonicalUrl} />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content={type} />
    <meta property="og:url" content={canonicalUrl} />
    <meta property="og:title" content={fullTitle} />
    <meta property="og:description" content={description} />
    <meta property="og:image" content={imageUrl} />
    <meta property="og:site_name" content={siteName} />
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content={canonicalUrl} />
    <meta property="twitter:title" content={fullTitle} />
    <meta property="twitter:description" content={description} />
    <meta property="twitter:image" content={imageUrl} />
    
    <!-- Article specific meta tags -->
    {type === 'article' && (
      <>
        {publishedTime && <meta property="article:published_time" content={publishedTime} />}
        {modifiedTime && <meta property="article:modified_time" content={modifiedTime} />}
        {author && <meta property="article:author" content={author} />}
        {tags.map((tag: string) => <meta property="article:tag" content={tag} />)}
      </>
    )}
    
    <!-- Structured Data -->
    <script type="application/ld+json" is:inline set:html={JSON.stringify({
      "@context": "https://schema.org",
      "@type": type === 'article' ? "BlogPosting" : "WebSite",
      "headline": title,
      "description": description,
      "image": imageUrl,
      "url": canonicalUrl,
      "datePublished": publishedTime,
      "dateModified": modifiedTime || publishedTime,
      "author": author ? {
        "@type": "Person",
        "name": author
      } : undefined,
      "publisher": {
        "@type": "Organization",
        "name": siteName,
        "url": siteUrl,
        "logo": {
          "@type": "ImageObject",
          "url": new URL('/favicon.svg', siteUrl).href
        }
      },
      "mainEntityOfPage": {
        "@type": "WebPage",
        "@id": canonicalUrl
      }
    })} />
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    
    <!-- Performance optimizations -->
    <link rel="dns-prefetch" href="https://supabase.voicehype.ai" />
    
    <!-- Theme detection script (inline for performance) -->
    <script is:inline>
      // Check for saved theme preference or default to 'light'
      const theme = localStorage.getItem('theme') || 'light';
      if (theme === 'dark') {
        document.documentElement.classList.add('dark');
      }
    </script>
  </head>
  
  <body class="dark:bg-gray-900 dark:text-gray-100 text-gray-900 transition-colors duration-200 bg-white">
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50 px-4 py-2 text-white bg-blue-600 rounded-md sr-only">
      Skip to main content
    </a>
    
    <div class="flex flex-col min-h-screen">
      <!-- Header -->
      <Navbar />
      
      <!-- Main content -->
      <main id="main-content" class="flex-1">
        <slot />
      </main>
      
      <!-- Footer -->
      <footer class="bg-gray-50 dark:bg-gray-800 dark:border-gray-700 border-t border-gray-200">
        <div class="max-w-7xl sm:px-6 lg:px-8 px-4 py-12 mx-auto">
          <div class="md:grid-cols-3 grid grid-cols-1 gap-8">
            <!-- About -->
            <div>
              <h3 class="mb-4 text-lg font-semibold">VoiceHype Blog</h3>
              <p class="dark:text-gray-400 mb-4 text-gray-600">
                Insights and tutorials on voice-to-prompt technology, AI development, and the future of developer tools.
              </p>
              <div class="flex space-x-4">
                <a href="https://voicehype.ai" target="_blank" rel="noopener noreferrer" class="dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-blue-600">
                  VoiceHype App
                </a>
                <a href="https://marketplace.visualstudio.com/items?itemName=voicehype.voicehype" target="_blank" rel="noopener noreferrer" class="dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-blue-600">
                  VS Code Extension
                </a>
              </div>
            </div>
            
            <!-- Quick Links -->
            <div>
              <h3 class="mb-4 text-lg font-semibold">Quick Links</h3>
              <ul class="space-y-2">
                <li><a href="/" class="dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 text-gray-600">Home</a></li>
                <li><a href="/about" class="dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 text-gray-600">About</a></li>
                <li><a href="/rss.xml" class="dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 text-gray-600">RSS Feed</a></li>
              </ul>
            </div>
            
            <!-- Contact -->
            <div>
              <h3 class="mb-4 text-lg font-semibold">Connect</h3>
              <p class="dark:text-gray-400 mb-2 text-gray-600">
                Stay updated with the latest from VoiceHype
              </p>
              <p class="dark:text-gray-500 text-sm text-gray-500">
                © 2024 VoiceHype. All rights reserved.
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>

  </body>
</html>
