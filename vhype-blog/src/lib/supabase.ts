import { createClient } from '@supabase/supabase-js'

// Supabase configuration
const supabaseUrl = 'https://supabase.voicehype.ai'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoiYW5vbiIsImlzcyI6ImFidWgxMjMxIiwiaWF0IjoxNzQ6Mzg1MjAwLCJleHAiOjE5MDQxNTE2MDB9.EkgKcDa0V0VEmPsXPmyal3YvxYuu9Q1k8OZZv7Gs8_o'

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: false, // We don't need auth persistence for public blog
  },
  db: {
    schema: 'blog'
  }
})

// Types for our blog data
export interface BlogArticle {
  id: string
  title: string
  slug: string
  content?: string
  excerpt: string
  cover_image_url?: string
  author_id: string
  status: 'draft' | 'published' | 'archived'
  published_at: string | null
  created_at: string
  updated_at: string
  meta_title?: string
  meta_description?: string
  featured: boolean
  view_count: number
  author_username: string
  author_name: string
  author_avatar?: string
  author_bio?: string
}

export interface BlogArticlePreview {
  id: string
  title: string
  slug: string
  excerpt: string
  cover_image_url?: string
  author_id: string
  published_at: string
  created_at: string
  meta_title?: string
  meta_description?: string
  featured: boolean
  view_count: number
  author_username: string
  author_name: string
  author_avatar?: string
}

// API functions
export async function getArticleBySlug(slug: string): Promise<BlogArticle | null> {
  try {
    const { data, error } = await supabase.rpc('get_article_with_view_increment', {
      slug_param: slug
    })

    if (error) {
      console.error('Error fetching article:', error)
      return null
    }

    return data?.[0] || null
  } catch (error) {
    console.error('Error fetching article:', error)
    return null
  }
}

export async function getPublishedArticles(
  limit: number = 10,
  offset: number = 0,
  orderBy: 'published_at' | 'view_count' | 'created_at' = 'published_at'
): Promise<BlogArticlePreview[]> {
  try {
    const { data, error } = await supabase.rpc('get_published_articles', {
      limit_count: limit,
      offset_count: offset,
      order_by: orderBy
    })

    if (error) {
      console.error('Error fetching articles:', error)
      return []
    }

    return data || []
  } catch (error) {
    console.error('Error fetching articles:', error)
    return []
  }
}

export async function searchPublishedArticles(searchTerm: string): Promise<BlogArticlePreview[]> {
  try {
    const { data, error } = await supabase.rpc('search_published_articles', {
      search_term: searchTerm
    })

    if (error) {
      console.error('Error searching articles:', error)
      return []
    }

    return data || []
  } catch (error) {
    console.error('Error searching articles:', error)
    return []
  }
}

export async function getFeaturedArticles(limit: number = 5): Promise<BlogArticlePreview[]> {
  try {
    const { data, error } = await supabase
      .from('articles')
      .select(`
        id,
        title,
        slug,
        excerpt,
        cover_image_url,
        author_id,
        published_at,
        created_at,
        meta_title,
        meta_description,
        featured,
        profiles:author_id (
          username,
          full_name,
          avatar_url
        )
      `)
      .eq('status', 'published')
      .eq('featured', true)
      .not('published_at', 'is', null)
      .order('published_at', { ascending: false })
      .limit(limit)

    if (error) {
      console.error('Error fetching featured articles:', error)
      return []
    }

    // Transform the data to match our interface
    return data?.map(article => ({
      id: article.id,
      title: article.title,
      slug: article.slug,
      excerpt: article.excerpt,
      cover_image_url: article.cover_image_url,
      author_id: article.author_id,
      published_at: article.published_at!,
      created_at: article.created_at,
      meta_title: article.meta_title,
      meta_description: article.meta_description,
      featured: article.featured,
      view_count: 0, // We'll get this from article_views if needed
      author_username: (article.profiles as any)?.username || '',
      author_name: (article.profiles as any)?.full_name || '',
      author_avatar: (article.profiles as any)?.avatar_url
    })) || []
  } catch (error) {
    console.error('Error fetching featured articles:', error)
    return []
  }
}

// Utility functions
export function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

export function calculateReadingTime(content: string): number {
  const wordsPerMinute = 200
  const wordCount = content.split(/\s+/).filter(word => word.length > 0).length
  return Math.ceil(wordCount / wordsPerMinute)
}

export function generateExcerpt(content: string, maxLength: number = 160): string {
  // Remove markdown syntax and HTML tags
  const plainText = content
    .replace(/#{1,6}\s+/g, '') // Remove headers
    .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold
    .replace(/\*(.*?)\*/g, '$1') // Remove italic
    .replace(/`(.*?)`/g, '$1') // Remove inline code
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // Remove links, keep text
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/\n+/g, ' ') // Replace newlines with spaces
    .trim()

  if (plainText.length <= maxLength) {
    return plainText
  }

  // Find the last complete word within the limit
  const truncated = plainText.substring(0, maxLength)
  const lastSpaceIndex = truncated.lastIndexOf(' ')
  
  if (lastSpaceIndex > 0) {
    return truncated.substring(0, lastSpaceIndex) + '...'
  }
  
  return truncated + '...'
}
