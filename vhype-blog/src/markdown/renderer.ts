import type { ASTNode, RendererOptions } from './types';
import Prism from 'prismjs';

// Import core dependencies first (order matters!)
import 'prismjs/components/prism-clike'; // Required for C-like languages
import 'prismjs/components/prism-markup-templating'; // Required for templating languages

// Import common languages (dependencies first)
import 'prismjs/components/prism-javascript';
import 'prismjs/components/prism-typescript';
import 'prismjs/components/prism-jsx';
import 'prismjs/components/prism-tsx';
import 'prismjs/components/prism-css';
import 'prismjs/components/prism-scss';
import 'prismjs/components/prism-json';
import 'prismjs/components/prism-markdown';
import 'prismjs/components/prism-bash';

// Python and related
import 'prismjs/components/prism-python';

// Java and C-family (require clike)
import 'prismjs/components/prism-java';
import 'prismjs/components/prism-c';
import 'prismjs/components/prism-cpp';
import 'prismjs/components/prism-csharp';

// PHP (requires markup-templating)
import 'prismjs/components/prism-php';

// Other languages
import 'prismjs/components/prism-sql';
import 'prismjs/components/prism-yaml';
import 'prismjs/components/prism-docker';
import 'prismjs/components/prism-go';
import 'prismjs/components/prism-rust';
import 'prismjs/components/prism-dart';
import 'prismjs/components/prism-kotlin';
import 'prismjs/components/prism-swift';

export class MarkdownRenderer {
  private options: RendererOptions;

  constructor(options: RendererOptions = {}) {
    this.options = {
      enableAstroComponents: true, // Changed from enableVueComponents
      baseUrl: '',
      ...options
    };
  }

  render(node: ASTNode): string {
    switch (node.type) {
      case 'document':
        return this.renderDocument(node);
      case 'heading':
        return this.renderHeading(node);
      case 'paragraph':
        return this.renderParagraph(node);
      case 'text':
        return this.renderText(node);
      case 'bold':
        return this.renderBold(node);
      case 'italic':
        return this.renderItalic(node);
      case 'code':
        return this.renderInlineCode(node);
      case 'codeBlock':
        return this.renderCodeBlock(node);
      case 'link':
        return this.renderLink(node);
      case 'image':
        return this.renderImage(node);
      case 'list':
        return this.renderList(node);
      case 'listItem':
        return this.renderListItem(node);
      case 'blockquote':
        return this.renderBlockquote(node);
      case 'horizontalRule':
        return this.renderHorizontalRule(node);
      case 'customComponent':
        return this.renderCustomComponent(node);
      case 'lineBreak':
        return this.renderLineBreak(node);
      default:
        return node.content || '';
    }
  }

  private renderDocument(node: ASTNode): string {
    if (!node.children) return '';
    
    // Render children without adding extra newlines between them
    // The lineBreak nodes will handle spacing
    const renderedChildren = node.children.map(child => this.render(child));
    return renderedChildren.join('');
  }

  private renderHeading(node: ASTNode): string {
    const level = node.metadata?.level || 1;
    const content = node.children 
      ? node.children.map(child => this.render(child)).join('')
      : node.content || '';
    
    return `<h${level}>${content}</h${level}>`;
  }

  private renderParagraph(node: ASTNode): string {
    const content = node.children 
      ? node.children.map(child => this.render(child)).join('')
      : node.content || '';
    
    return `<p>${content}</p>`;
  }

  private renderText(node: ASTNode): string {
    return this.escapeHtml(node.content || '');
  }

  private renderBold(node: ASTNode): string {
    const content = node.children 
      ? node.children.map(child => this.render(child)).join('')
      : '';
    
    return `<strong>${content}</strong>`;
  }

  private renderItalic(node: ASTNode): string {
    const content = node.children 
      ? node.children.map(child => this.render(child)).join('')
      : '';
    
    return `<em>${content}</em>`;
  }

  private renderInlineCode(node: ASTNode): string {
    return `<code>${this.escapeHtml(node.content || '')}</code>`;
  }

  private renderCodeBlock(node: ASTNode): string {
    const language = node.metadata?.language || '';
    const content = node.content || '';

    // Normalize language name (handle common aliases)
    const normalizedLanguage = this.normalizeLanguage(language);

    if (normalizedLanguage && this.isLanguageSupported(normalizedLanguage)) {
      try {
        const highlightedCode = Prism.highlight(content, Prism.languages[normalizedLanguage], normalizedLanguage);
        return `<pre class="language-${normalizedLanguage}"><code class="language-${normalizedLanguage}">${highlightedCode}</code></pre>`;
      } catch (error) {
        console.warn(`Failed to highlight code for language: ${normalizedLanguage}`, error);
        // Fallback to escaped HTML with language class for styling
        return `<pre class="language-${normalizedLanguage}"><code class="language-${normalizedLanguage}">${this.escapeHtml(content)}</code></pre>`;
      }
    }

    // No language specified or language not supported
    return `<pre><code>${this.escapeHtml(content)}</code></pre>`;
  }

  private normalizeLanguage(language: string): string {
    const aliases: Record<string, string> = {
      'js': 'javascript',
      'ts': 'typescript',
      'py': 'python',
      'sh': 'bash',
      'shell': 'bash',
      'yml': 'yaml',
      'dockerfile': 'docker',
      'cs': 'csharp',
      'c++': 'cpp',
      'c#': 'csharp'
    };

    return aliases[language.toLowerCase()] || language.toLowerCase();
  }

  private isLanguageSupported(language: string): boolean {
    try {
      return !!(Prism.languages && Prism.languages[language]);
    } catch (error) {
      console.warn(`Error checking language support for: ${language}`, error);
      return false;
    }
  }

  private renderLink(node: ASTNode): string {
    const href = node.attributes?.href || '#';
    const content = node.content || 'Link';
    
    return `<a href="${this.escapeAttribute(href)}">${this.escapeHtml(content)}</a>`;
  }

  private renderImage(node: ASTNode): string {
    const src = node.attributes?.src || '';
    const alt = node.attributes?.alt || '';
    
    return `<img src="${this.escapeAttribute(src)}" alt="${this.escapeAttribute(alt)}" />`;
  }

  private renderList(node: ASTNode): string {
    if (!node.children) return '';
    
    const items = node.children.map(child => this.render(child)).join('\n');
    return `<ul>\n${items}\n</ul>`;
  }

  private renderListItem(node: ASTNode): string {
    const content = node.children 
      ? node.children.map(child => this.render(child)).join('')
      : node.content || '';
    
    return `<li>${content}</li>`;
  }

  private renderBlockquote(node: ASTNode): string {
    const content = node.children 
      ? node.children.map(child => this.render(child)).join('')
      : node.content || '';
    
    return `<blockquote>${content}</blockquote>`;
  }

  private renderHorizontalRule(node: ASTNode): string {
    return `<hr />`;
  }

  private renderCustomComponent(node: ASTNode): string {
    if (!this.options.enableAstroComponents) {
      return `<!-- Custom Component: ${node.metadata?.componentName || 'Unknown'} -->`;
    }
    
    const componentName = node.metadata?.componentName || 'UnknownComponent';
    const attributes = node.attributes || {};
    
    const attrString = Object.entries(attributes)
      .map(([key, value]) => `${key}="${this.escapeAttribute(String(value))}"`)
      .join(' ');
    
    if (attrString) {
      return `<${componentName} ${attrString} />`;
    }
    
    return `<${componentName} />`;
  }

  private renderLineBreak(node: ASTNode): string {
    return '<br>';
  }

  private escapeHtml(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');
  }

  private escapeAttribute(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;');
  }
}
