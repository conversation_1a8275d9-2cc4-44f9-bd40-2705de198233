// Markdown AST node types
export type NodeType =
  | 'document'
  | 'heading'
  | 'paragraph'
  | 'text'
  | 'bold'
  | 'italic'
  | 'code'
  | 'codeBlock'
  | 'link'
  | 'image'
  | 'list'
  | 'listItem'
  | 'blockquote'
  | 'horizontalRule'
  | 'customComponent'
  | 'lineBreak';
  
// Token types for lexer
export type TokenType = 
  | 'text'
  | 'heading'
  | 'bold'
  | 'italic'
  | 'code'
  | 'codeBlock'
  | 'link'
  | 'image'
  | 'list'
  | 'blockquote'
  | 'horizontalRule'
  | 'customComponent'
  | 'newline'
  | 'whitespace'
  | 'eof';

export interface Token {
  type: TokenType;
  value: string;
  position: number;
  metadata?: Record<string, any>;
}

export interface ASTNode {
  type: NodeType;
  content?: string;
  children?: ASTNode[];
  attributes?: Record<string, string>;
  metadata?: Record<string, any>;
}

export interface LexerOptions {
  enableCustomComponents?: boolean;
  customComponentPattern?: RegExp;
}

export interface ParserOptions {
  enableCustomComponents?: boolean;
}

export interface RendererOptions {
  enableAstroComponents?: boolean; // Changed from enableVueComponents
  baseUrl?: string;
}
