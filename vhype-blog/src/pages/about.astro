---
import BaseLayout from '../layouts/BaseLayout.astro';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
---

<BaseLayout
  title="About VoiceHype Blog"
  description="Learn about VoiceHype Blog, our mission to advance voice-to-prompt technology, and how we're helping developers work more efficiently with AI tools."
>
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <!-- Hero Section -->
    <div class="text-center mb-12">
      <h1 class="text-4xl sm:text-5xl font-bold text-foreground mb-6">
        About VoiceHype Blog
      </h1>
      <p class="text-xl text-muted-foreground max-w-3xl mx-auto">
        Advancing the future of developer productivity through voice-to-prompt technology and AI innovation.
      </p>
    </div>

    <!-- Main Content -->
    <div class="prose prose-lg dark:prose-invert max-w-none">
      <!-- Mission Section -->
      <section class="mb-12">
        <h2 class="text-3xl font-semibold text-foreground mb-6">
          Our Mission
        </h2>
        <p class="text-muted-foreground leading-relaxed mb-6">
          VoiceHype Blog is dedicated to exploring and advancing voice-to-prompt technology for developers.
          We believe that the future of software development lies in seamless human-AI collaboration,
          where developers can express their ideas naturally through voice and receive optimized,
          contextual assistance from AI tools.
        </p>
        <p class="text-muted-foreground leading-relaxed">
          Our content focuses on practical insights, tutorials, and thought leadership in the rapidly
          evolving landscape of AI-assisted development, with a special emphasis on voice interfaces
          and prompt engineering.
        </p>
      </section>

      <!-- VoiceHype Product -->
      <section class="mb-12">
        <h2 class="text-3xl font-semibold text-foreground mb-6">
          About VoiceHype
        </h2>
        <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950 dark:to-indigo-950 border-blue-200 dark:border-blue-800">
          <CardContent className="p-8">
          <div class="flex items-start gap-6">
            <div class="bg-blue-500 text-white p-4 rounded-lg">
              <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
              </svg>
            </div>
            <div class="flex-1">
              <h3 class="text-2xl font-semibold text-blue-900 dark:text-blue-100 mb-4">
                VoiceHype: Voice-to-Prompt for Developers
              </h3>
              <p class="text-blue-800 dark:text-blue-200 mb-6 leading-relaxed">
                VoiceHype is our flagship product that transforms your voice into optimized prompts for
                Large Language Models (LLMs). Designed specifically for developers, it integrates seamlessly
                with your workflow through our VS Code extension and web application.
              </p>
              <div class="flex flex-wrap gap-4">
                <Button asChild>
                  <a
                    href="https://voicehype.ai"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                    Try VoiceHype
                  </a>
                </Button>
                <Button variant="secondary" asChild>
                  <a
                    href="https://marketplace.visualstudio.com/items?itemName=voicehype.voicehype"
                    target="_blank"
                    rel="noopener noreferrer"
                >
                  <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                    VS Code Extension
                  </a>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      <!-- Contact -->
      <section class="mb-12">
        <h2 class="text-3xl font-semibold text-foreground mb-6">
          Get in Touch
        </h2>
        <p class="text-muted-foreground leading-relaxed mb-6">
          We love hearing from our community! Whether you have questions about voice-to-prompt
          technology, suggestions for blog topics, or feedback on VoiceHype, we're always eager
          to connect with fellow developers and AI enthusiasts.
        </p>
        <Card>
          <CardContent className="p-6">
            <h3 class="text-lg font-semibold text-foreground mb-4">
              Connect with us:
            </h3>
            <ul class="space-y-2 text-muted-foreground">
              <li>• Visit our main website: <a href="https://voicehype.ai" target="_blank" rel="noopener noreferrer" class="text-primary hover:underline">voicehype.ai</a></li>
              <li>• Try our VS Code extension from the <a href="https://marketplace.visualstudio.com/items?itemName=voicehype.voicehype" target="_blank" rel="noopener noreferrer" class="text-primary hover:underline">Visual Studio Marketplace</a></li>
              <li>• Follow our blog for the latest insights and tutorials</li>
            </ul>
          </CardContent>
        </Card>
      </section>
    </div>
  </div>
</BaseLayout>