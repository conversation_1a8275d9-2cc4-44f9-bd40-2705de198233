---
import BlogPost from '../layouts/BlogPost.astro';
import { getArticleBySlug } from '../lib/supabase';
import type { BlogArticle } from '../lib/supabase';

export async function getStaticPaths() {
  // For now, return empty array since we're using SSR for dynamic content
  // In production, you might want to pre-generate popular articles
  return [];
}

const { slug } = Astro.params;

if (!slug) {
  return Astro.redirect('/404');
}

// Fetch article from Supabase
let article: BlogArticle | null = null;
let error: string | null = null;

try {
  article = await getArticleBySlug(slug);
} catch (e) {
  console.error('Error fetching article:', e);
  error = 'Failed to load article';
}

// Handle article not found
if (!article) {
  return Astro.redirect('/404');
}
---

<BlogPost article={article} />
