---
import BaseLayout from '../layouts/BaseLayout.astro';
---

<BaseLayout
  title="Page Not Found"
  description="The page you're looking for doesn't exist."
>
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
    <div class="text-center">
      <!-- 404 Icon -->
      <div class="mb-8">
        <svg class="mx-auto h-24 w-24 text-gray-400 dark:text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.29-1.009-5.824-2.562M15 11.172a4 4 0 00-5.656 0M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      </div>
      
      <!-- 404 Text -->
      <h1 class="text-6xl font-bold text-gray-900 dark:text-gray-100 mb-4">
        404
      </h1>
      
      <h2 class="text-2xl font-semibold text-gray-700 dark:text-gray-300 mb-4">
        Article Not Found
      </h2>
      
      <p class="text-lg text-gray-600 dark:text-gray-400 mb-8 max-w-md mx-auto">
        Sorry, we couldn't find the article you're looking for. It might have been moved, deleted, or the URL might be incorrect.
      </p>
      
      <!-- Action Buttons -->
      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <a 
          href="/"
          class="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
          </svg>
          Back to Home
        </a>
        
        <a 
          href="https://voicehype.ai"
          target="_blank"
          rel="noopener noreferrer"
          class="inline-flex items-center px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg transition-colors"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
          </svg>
          Try VoiceHype
        </a>
      </div>
      
      <!-- Search Suggestion -->
      <div class="mt-12">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Looking for something specific?
        </h3>
        <p class="text-gray-600 dark:text-gray-400 mb-4">
          Try searching our latest articles or browse by category.
        </p>
        
        <!-- Simple search form -->
        <form class="max-w-md mx-auto" action="/" method="get">
          <div class="flex">
            <input 
              type="text" 
              name="search"
              placeholder="Search articles..."
              class="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100"
            />
            <button 
              type="submit"
              class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-r-lg transition-colors"
            >
              Search
            </button>
          </div>
        </form>
      </div>
      
      <!-- Popular Articles Suggestion -->
      <div class="mt-12">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Popular Articles
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl mx-auto">
          <a 
            href="/"
            class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-blue-500 dark:hover:border-blue-400 transition-colors"
          >
            <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">
              Getting Started with VoiceHype
            </h4>
            <p class="text-sm text-gray-600 dark:text-gray-400">
              Learn how to use voice-to-prompt technology for better AI interactions.
            </p>
          </a>
          
          <a 
            href="/"
            class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-blue-500 dark:hover:border-blue-400 transition-colors"
          >
            <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">
              AI Development Tips
            </h4>
            <p class="text-sm text-gray-600 dark:text-gray-400">
              Best practices for working with LLMs and AI tools in development.
            </p>
          </a>
        </div>
      </div>
    </div>
  </div>
</BaseLayout>
