---
import BaseLayout from '../layouts/BaseLayout.astro';
import ArticleCard from '../components/ArticleCard.astro';
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { getPublishedArticles, getFeaturedArticles, formatDate } from '../lib/supabase';
import type { BlogArticlePreview } from '../lib/supabase';

// Fetch articles for different sections
let newestArticles: BlogArticlePreview[] = [];
let popularArticles: BlogArticlePreview[] = [];
let featuredArticles: BlogArticlePreview[] = [];
let error: string | null = null;

try {
  // Fetch newest articles (5 most recent)
  newestArticles = await getPublishedArticles(5, 0, 'published_at');

  // Fetch popular articles (5 most viewed)
  popularArticles = await getPublishedArticles(5, 0, 'view_count');

  // Fetch featured articles
  featuredArticles = await getFeaturedArticles(3);
} catch (e) {
  console.error('Error fetching articles:', e);
  error = 'Failed to load articles';
}

const hasArticles = newestArticles.length > 0 || popularArticles.length > 0 || featuredArticles.length > 0;
---

<BaseLayout
  title="VoiceHype Blog - Voice-to-Prompt Technology for Developers"
  description="Discover the latest insights on voice-to-prompt technology, AI development, and developer tools. Learn how VoiceHype is revolutionizing the way developers interact with AI."
>
  <!-- Hero Section -->
  <section class="bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-950 dark:to-indigo-950 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center">
        <h1 class="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          VoiceHype Blog
        </h1>
        <p class="text-xl sm:text-2xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
          Insights and tutorials on voice-to-prompt technology, AI development, and the future of developer tools.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <Button asChild size="lg">
            <a
              href="https://voicehype.ai"
              target="_blank"
              rel="noopener noreferrer"
            >
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
              </svg>
              Try VoiceHype
            </a>
          </Button>
          <Button variant="secondary" size="lg" asChild>
            <a
              href="https://marketplace.visualstudio.com/items?itemName=voicehype.voicehype"
              target="_blank"
              rel="noopener noreferrer"
            >
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              VS Code Extension
            </a>
          </Button>
        </div>
      </div>
    </div>
  </section>

  <!-- Main Content -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    {error && (
      <Alert variant="destructive" className="mb-8">
        <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
        </svg>
        <AlertTitle>Error loading articles</AlertTitle>
        <AlertDescription>
          {error}
        </AlertDescription>
      </Alert>
    )}

    {!hasArticles && !error && (
      <!-- Coming Soon Section -->
      <section class="text-center py-16">
        <div class="max-w-2xl mx-auto">
          <div class="mb-8">
            <svg class="mx-auto h-24 w-24 text-muted-foreground" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
            </svg>
          </div>
          <h2 class="text-3xl font-bold text-foreground mb-4">
            Coming Soon
          </h2>
          <p class="text-lg text-muted-foreground mb-8">
            We're working on amazing content about voice-to-prompt technology and AI development. Stay tuned for our first articles!
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" asChild>
              <a
                href="https://voicehype.ai"
                target="_blank"
                rel="noopener noreferrer"
              >
                Try VoiceHype Now
              </a>
            </Button>
          </div>
        </div>
      </section>
    )}

    {hasArticles && (
      <>
        <!-- Featured Articles Section -->
        {featuredArticles.length > 0 && (
          <section class="mb-16">
            <h2 class="text-3xl font-bold text-foreground mb-8">
              Featured Articles
            </h2>
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {featuredArticles.map((article) => (
                <ArticleCard article={article} />
              ))}
            </div>
          </section>
        )}

        <!-- Newest Articles Section -->
        {newestArticles.length > 0 && (
          <section class="mb-16">
            <h2 class="text-3xl font-bold text-foreground mb-8">
              Latest Articles
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {newestArticles.map((article) => (
                <ArticleCard article={article} />
              ))}
            </div>
          </section>
        )}

        <!-- Popular Articles Section -->
        {popularArticles.length > 0 && popularArticles.some(article => !newestArticles.find(newest => newest.id === article.id)) && (
          <section class="mb-16">
            <h2 class="text-3xl font-bold text-foreground mb-8">
              Popular Articles
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {popularArticles.filter(article => !newestArticles.find(newest => newest.id === article.id)).map((article) => (
                <ArticleCard article={article} />
              ))}
            </div>
          </section>
        )}
      </>
    )}
  </div>
</BaseLayout>
