# VoiceHype Blog Deployment Guide

This guide covers deploying the VoiceHype blog to Netlify and configuring DNS for the `blog.voicehype.ai` subdomain.

## Prerequisites

1. **Netlify Account**: Ensure you have a Netlify account
2. **Domain Control**: Access to voicehype.ai DNS settings in Netlify
3. **Database Setup**: Supabase database with the blog schema and RLS policies configured
4. **Environment Variables**: Properly configured environment variables

## Database Setup

Before deploying, run the database cleanup script:

```sql
-- Run the SQL commands from database-cleanup.sql
-- This will:
-- 1. Remove article_reactions and reactions tables
-- 2. Set up RLS policies to hide drafts from public
-- 3. Create helper functions for fetching articles
```

## Netlify Deployment Steps

### 1. Create New Netlify Site

1. Go to [Netlify Dashboard](https://app.netlify.com/)
2. Click "Add new site" → "Import an existing project"
3. Connect to your Git repository containing the blog code
4. Select the repository and branch (usually `main`)

### 2. Build Configuration

Configure the build settings:

```yaml
# netlify.toml
[build]
  command = "npm run build"
  publish = "dist"

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/*"
  to = "/404"
  status = 404

# Handle client-side routing for search
[[redirects]]
  from = "/search"
  to = "/search/index.html"
  status = 200

# RSS feed
[[redirects]]
  from = "/rss.xml"
  to = "/rss.xml"
  status = 200
```

### 3. Environment Variables

Set the following environment variables in Netlify:

```bash
PUBLIC_SUPABASE_URL=https://supabase.voicehype.ai
PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************.EkgKcDa0V0VEmPsXPmyal3YvxYuu9Q1k8OZZv7Gs8_o
PUBLIC_SITE_URL=https://blog.voicehype.ai
PUBLIC_SITE_NAME=VoiceHype Blog
PUBLIC_SITE_DESCRIPTION=Voice-to-Prompt Technology Blog for Developers
```

### 4. Deploy

1. Click "Deploy site"
2. Wait for the build to complete
3. Note the temporary Netlify URL (e.g., `https://amazing-name-123456.netlify.app`)

## DNS Configuration

Since you already have `voicehype.ai` configured with Netlify DNS, you need to add a subdomain record.

### 1. Access Netlify DNS Settings

1. Go to your main VoiceHype site in Netlify
2. Navigate to "Domain settings" → "DNS records"
3. You should see existing DNS records for `voicehype.ai`

### 2. Add CNAME Record for Blog Subdomain

Add a new DNS record:

```
Type: CNAME
Name: blog
Value: [your-blog-site-name].netlify.app
TTL: 3600 (or default)
```

For example, if your blog site URL is `voicehype-blog.netlify.app`, the CNAME record would be:

```
Type: CNAME
Name: blog
Value: voicehype-blog.netlify.app
```

### 3. Configure Custom Domain in Blog Site

1. Go to your blog site in Netlify
2. Navigate to "Domain settings" → "Custom domains"
3. Click "Add custom domain"
4. Enter: `blog.voicehype.ai`
5. Netlify will verify the domain and issue an SSL certificate

## Alternative: Subdomain Delegation

If you prefer to manage the blog site independently:

### 1. Create NS Records

In your main voicehype.ai DNS settings, add NS records:

```
Type: NS
Name: blog
Value: dns1.p01.nsone.net
Value: dns2.p01.nsone.net
Value: dns3.p01.nsone.net
Value: dns4.p01.nsone.net
```

### 2. Configure Blog Site DNS

1. In the blog site, go to "Domain settings" → "DNS records"
2. Add the domain `blog.voicehype.ai`
3. Netlify will manage DNS for the subdomain

## Verification Steps

After deployment and DNS configuration:

### 1. Test the Deployment

1. Visit `https://blog.voicehype.ai`
2. Verify the homepage loads correctly
3. Test navigation between pages
4. Check that search functionality works
5. Verify article pages load (if you have published articles)

### 2. Test SEO Elements

1. Check page titles and meta descriptions
2. Verify Open Graph tags using [Facebook Debugger](https://developers.facebook.com/tools/debug/)
3. Test structured data using [Google Rich Results Test](https://search.google.com/test/rich-results)
4. Verify sitemap at `https://blog.voicehype.ai/sitemap-index.xml`

### 3. Performance Testing

1. Test page load speeds using [PageSpeed Insights](https://pagespeed.web.dev/)
2. Verify mobile responsiveness
3. Check accessibility using browser dev tools

## Troubleshooting

### Common Issues

1. **DNS Propagation**: DNS changes can take up to 48 hours to propagate globally
2. **SSL Certificate**: Netlify automatically provisions SSL certificates, but it may take a few minutes
3. **Build Failures**: Check build logs in Netlify for specific error messages
4. **Environment Variables**: Ensure all required environment variables are set correctly

### Debug Commands

```bash
# Test DNS resolution
nslookup blog.voicehype.ai

# Check SSL certificate
openssl s_client -connect blog.voicehype.ai:443 -servername blog.voicehype.ai

# Test API connectivity
curl -X GET "https://supabase.voicehype.ai/rest/v1/articles?select=*&status=eq.published" \
  -H "apikey: YOUR_ANON_KEY" \
  -H "Authorization: Bearer YOUR_ANON_KEY"
```

## Post-Deployment Tasks

1. **Submit Sitemap**: Submit the sitemap to Google Search Console
2. **Analytics**: Set up Google Analytics or other analytics tools
3. **Monitoring**: Set up uptime monitoring for the blog
4. **Content**: Start publishing articles using the blog editor
5. **Social Media**: Update social media profiles with the new blog URL

## Security Considerations

1. **RLS Policies**: Ensure Row Level Security policies are properly configured
2. **API Keys**: Never expose service role keys on the client side
3. **HTTPS**: Ensure all traffic is served over HTTPS
4. **Headers**: Consider adding security headers via Netlify configuration

## Maintenance

1. **Regular Updates**: Keep dependencies updated
2. **Backup**: Regularly backup your Supabase database
3. **Monitoring**: Monitor site performance and uptime
4. **Content Moderation**: Implement content review processes

---

## Quick Reference

- **Blog URL**: https://blog.voicehype.ai
- **Netlify Dashboard**: https://app.netlify.com/
- **Supabase Dashboard**: https://supabase.voicehype.ai/
- **DNS Management**: Via Netlify DNS for voicehype.ai

For any issues, refer to the Netlify documentation or contact support.
