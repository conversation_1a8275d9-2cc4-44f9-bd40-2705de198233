create table blog.article_views (
  article_id uuid not null,
  view_count integer null default 0,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  constraint article_views_pkey primary key (article_id),
  constraint article_views_article_id_fkey foreign KEY (article_id) references blog.articles (id) on delete CASCADE
) TABLESPACE pg_default;

create index IF not exists idx_article_views_article_id on blog.article_views using btree (article_id) TABLESPACE pg_default;

create table blog.articles (
  id uuid not null default gen_random_uuid (),
  title text not null,
  slug text not null,
  content text null,
  excerpt text null,
  cover_image_url text null,
  author_id uuid not null,
  status text null default 'draft'::text,
  published_at timestamp with time zone null,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  meta_title text null,
  meta_description text null,
  featured boolean null default false,
  constraint articles_pkey primary key (id),
  constraint articles_slug_key unique (slug),
  constraint articles_author_id_fkey foreign KEY (author_id) references blog.profiles (id) on delete CASCADE,
  constraint articles_status_check check (
    (
      status = any (
        array[
          'draft'::text,
          'published'::text,
          'archived'::text
        ]
      )
    )
  )
) TABLESPACE pg_default;

create index IF not exists idx_articles_slug on blog.articles using btree (slug) TABLESPACE pg_default;

create index IF not exists idx_articles_status on blog.articles using btree (status) TABLESPACE pg_default;

create index IF not exists idx_articles_published_at on blog.articles using btree (published_at) TABLESPACE pg_default;

create index IF not exists idx_articles_author_id on blog.articles using btree (author_id) TABLESPACE pg_default;

create index IF not exists idx_articles_featured on blog.articles using btree (featured) TABLESPACE pg_default;

create table blog.categories (
  id uuid not null default gen_random_uuid (),
  name text not null,
  slug text not null,
  description text null,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  constraint categories_pkey primary key (id),
  constraint categories_name_key unique (name),
  constraint categories_slug_key unique (slug)
) TABLESPACE pg_default;

create table blog.newsletter_subscribers (
  id uuid not null default gen_random_uuid (),
  email text not null,
  name text null,
  subscribed boolean null default true,
  created_at timestamp with time zone null default now(),
  unsubscribed_at timestamp with time zone null,
  constraint newsletter_subscribers_pkey primary key (id),
  constraint newsletter_subscribers_email_key unique (email)
) TABLESPACE pg_default;

create table blog.tags (
  id uuid not null default gen_random_uuid (),
  name text not null,
  slug text not null,
  description text null,
  created_at timestamp with time zone null default now(),
  updated_at timestamp with time zone null default now(),
  constraint tags_pkey primary key (id),
  constraint tags_name_key unique (name),
  constraint tags_slug_key unique (slug)
) TABLESPACE pg_default;

create table blog.profiles (
  id uuid not null,
  updated_at timestamp with time zone null default now(),
  username text null,
  full_name text null,
  avatar_url text null,
  website text null,
  role text null default 'reader'::text,
  bio text null,
  created_at timestamp with time zone null default now(),
  constraint profiles_pkey primary key (id),
  constraint profiles_username_key unique (username),
  constraint profiles_id_fkey foreign KEY (id) references auth.users (id) on delete CASCADE,
  constraint profiles_role_check check (
    (
      role = any (
        array[
          'admin'::text,
          'editor'::text,
          'author'::text,
          'reader'::text
        ]
      )
    )
  ),
  constraint username_length check ((char_length(username) >= 3))
) TABLESPACE pg_default;

create table blog.article_tags (
  article_id uuid not null,
  tag_id uuid not null,
  constraint article_tags_pkey primary key (article_id, tag_id),
  constraint article_tags_article_id_fkey foreign KEY (article_id) references blog.articles (id) on delete CASCADE,
  constraint article_tags_tag_id_fkey foreign KEY (tag_id) references blog.tags (id) on delete CASCADE
) TABLESPACE pg_default;

create index IF not exists idx_article_tags_article_id on blog.article_tags using btree (article_id) TABLESPACE pg_default;

create index IF not exists idx_article_tags_tag_id on blog.article_tags using btree (tag_id) TABLESPACE pg_default;

create table blog.article_reactions (
  id uuid not null default gen_random_uuid (),
  article_id uuid not null,
  user_id uuid null,
  created_at timestamp with time zone null default now(),
  constraint article_reactions_pkey primary key (id),
  constraint unique_article_user_reaction unique (article_id, user_id),
  constraint article_reactions_article_id_fkey foreign KEY (article_id) references blog.articles (id) on delete CASCADE,
  constraint article_reactions_user_id_fkey foreign KEY (user_id) references blog.profiles (id) on delete CASCADE
) TABLESPACE pg_default;

create index IF not exists idx_article_reactions_article_id on blog.article_reactions using btree (article_id) TABLESPACE pg_default;

create index IF not exists idx_article_reactions_user_id on blog.article_reactions using btree (user_id) TABLESPACE pg_default;

create table blog.article_categories (
  article_id uuid not null,
  category_id uuid not null,
  constraint article_categories_pkey primary key (article_id, category_id),
  constraint article_categories_article_id_fkey foreign KEY (article_id) references blog.articles (id) on delete CASCADE,
  constraint article_categories_category_id_fkey foreign KEY (category_id) references blog.categories (id) on delete CASCADE
) TABLESPACE pg_default;

create index IF not exists idx_article_categories_article_id on blog.article_categories using btree (article_id) TABLESPACE pg_default;

create index IF not exists idx_article_categories_category_id on blog.article_categories using btree (category_id) TABLESPACE pg_default;
