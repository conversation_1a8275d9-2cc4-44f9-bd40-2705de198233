[{"schema_name": "blog", "table_name": "article_reactions", "policy_name": "Reactions are viewable by everyone", "command": "r", "permissive": true, "using_expression": "(EXISTS ( SELECT 1\n   FROM blog.articles\n  WHERE ((articles.id = article_reactions.article_id) AND (articles.status = 'published'::text))))", "with_check_expression": null}, {"schema_name": "blog", "table_name": "article_reactions", "policy_name": "Users can delete their own reactions", "command": "d", "permissive": true, "using_expression": "(uid() = user_id)", "with_check_expression": null}, {"schema_name": "blog", "table_name": "article_reactions", "policy_name": "Users can insert their own reactions", "command": "a", "permissive": true, "using_expression": null, "with_check_expression": "((uid() = user_id) AND (EXISTS ( SELECT 1\n   FROM blog.articles\n  WHERE ((articles.id = article_reactions.article_id) AND (articles.status = 'published'::text)))))"}, {"schema_name": "blog", "table_name": "article_tags", "policy_name": "Article tags are viewable by everyone", "command": "r", "permissive": true, "using_expression": "(EXISTS ( SELECT 1\n   FROM blog.articles\n  WHERE ((articles.id = article_tags.article_id) AND (articles.status = 'published'::text))))", "with_check_expression": null}, {"schema_name": "blog", "table_name": "article_tags", "policy_name": "Authors can manage tags for their articles", "command": "*", "permissive": true, "using_expression": "(EXISTS ( SELECT 1\n   FROM blog.articles\n  WHERE ((articles.id = article_tags.article_id) AND (articles.author_id = uid()))))", "with_check_expression": null}, {"schema_name": "blog", "table_name": "article_views", "policy_name": "Article views are viewable by everyone", "command": "r", "permissive": true, "using_expression": "(EXISTS ( SELECT 1\n   FROM blog.articles\n  WHERE ((articles.id = article_views.article_id) AND (articles.status = 'published'::text))))", "with_check_expression": null}, {"schema_name": "blog", "table_name": "articles", "policy_name": "Authors can create articles", "command": "a", "permissive": true, "using_expression": null, "with_check_expression": "((uid() = author_id) AND (EXISTS ( SELECT 1\n   FROM blog.profiles\n  WHERE ((profiles.id = uid()) AND (profiles.role = ANY (ARRAY['admin'::text, 'editor'::text, 'writer'::text, 'author'::text]))))))"}, {"schema_name": "blog", "table_name": "articles", "policy_name": "Authors can delete their own articles", "command": "d", "permissive": true, "using_expression": "((uid() = author_id) OR (EXISTS ( SELECT 1\n   FROM blog.profiles\n  WHERE ((profiles.id = uid()) AND (profiles.role = 'admin'::text)))))", "with_check_expression": null}, {"schema_name": "blog", "table_name": "articles", "policy_name": "Authors can update their own articles", "command": "w", "permissive": true, "using_expression": "(((uid() = author_id) AND (EXISTS ( SELECT 1\n   FROM blog.profiles\n  WHERE ((profiles.id = uid()) AND (profiles.role = ANY (ARRAY['writer'::text, 'author'::text])))))) OR (EXISTS ( SELECT 1\n   FROM blog.profiles\n  WHERE ((profiles.id = uid()) AND (profiles.role = ANY (ARRAY['admin'::text, 'editor'::text]))))))", "with_check_expression": null}, {"schema_name": "blog", "table_name": "articles", "policy_name": "Authors can view their own articles", "command": "r", "permissive": true, "using_expression": "((uid() = author_id) OR (EXISTS ( SELECT 1\n   FROM blog.profiles\n  WHERE ((profiles.id = uid()) AND (profiles.role = ANY (ARRAY['admin'::text, 'editor'::text]))))))", "with_check_expression": null}, {"schema_name": "blog", "table_name": "articles", "policy_name": "Published articles are viewable by everyone", "command": "r", "permissive": true, "using_expression": "(status = 'published'::text)", "with_check_expression": null}, {"schema_name": "blog", "table_name": "newsletter_subscribers", "policy_name": "Newsletter subscribers are viewable by admins only", "command": "r", "permissive": true, "using_expression": "(EXISTS ( SELECT 1\n   FROM blog.profiles\n  WHERE ((profiles.id = uid()) AND (profiles.role = 'admin'::text))))", "with_check_expression": null}, {"schema_name": "blog", "table_name": "profiles", "policy_name": "Users can update their own profile", "command": "w", "permissive": true, "using_expression": "(uid() = id)", "with_check_expression": null}, {"schema_name": "blog", "table_name": "profiles", "policy_name": "Users can view their own profile", "command": "r", "permissive": true, "using_expression": "(uid() = id)", "with_check_expression": null}, {"schema_name": "blog", "table_name": "tags", "policy_name": "Admins and editors can manage tags", "command": "*", "permissive": true, "using_expression": "(EXISTS ( SELECT 1\n   FROM blog.profiles\n  WHERE ((profiles.id = uid()) AND (profiles.role = ANY (ARRAY['admin'::text, 'editor'::text])))))", "with_check_expression": null}, {"schema_name": "blog", "table_name": "tags", "policy_name": "Tags are viewable by everyone", "command": "r", "permissive": true, "using_expression": "true", "with_check_expression": null}]